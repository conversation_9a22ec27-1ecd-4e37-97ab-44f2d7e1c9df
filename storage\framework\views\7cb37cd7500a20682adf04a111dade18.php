<?php $__env->startSection('title', $product->name . ' - Hermosart'); ?>

<?php $__env->startSection('content'); ?>
    <div class="bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <!-- Back button -->
            <div class="mb-6">
                <a href="<?php echo e(route('products')); ?>"
                    class="inline-flex items-center text-sm font-medium text-[#710d17] hover:text-[#9a2c39]">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    Back to Products
                </a>
            </div>

            <!-- Product detail section -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                <!-- Product image -->
                <div class="grid bg-gray-100 rounded-lg overflow-hidden justify-center items-center">
                    <?php if($product->image && Storage::exists('public/' . $product->image)): ?>
                        <img src="<?php echo e(asset($product->image)); ?>" alt="<?php echo e($product->name); ?>"
                            class="w-full h-auto object-cover">
                    <?php else: ?>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-gray-400" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                        </svg>
                    <?php endif; ?>
                </div>

                <!-- Product info -->
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2"><?php echo e($product->name); ?></h1>

                    <div class="flex items-center mb-4">
                        <div class="flex items-center">
                            <?php for($i = 0; $i < 5; $i++): ?>
                                <svg xmlns="http://www.w3.org/2000/svg"
                                    class="h-5 w-5 <?php echo e($i < ($product->rating ?? 4) ? 'text-yellow-400' : 'text-gray-300'); ?>"
                                    viewBox="0 0 20 20" fill="currentColor">
                                    <path
                                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                </svg>
                            <?php endfor; ?>
                        </div>
                        <span class="ml-2 text-sm text-gray-600"><?php echo e($product->rating); ?> (<?php echo e($product->sales); ?> sales)</span>
                    </div>

                    <div class="text-3xl font-bold text-[#710d17] mb-6">$<?php echo e(number_format($product->price, 2)); ?></div>

                    <div class="prose prose-sm text-gray-700 mb-8">
                        <p><?php echo e($product->description); ?></p>
                    </div>

                    <div class="border-t border-gray-200 pt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Product Details</h3>
                        <ul class="list-disc pl-5 space-y-2 text-sm text-gray-600">
                            <li>Digital download - instant delivery</li>
                            <li>Compatible with all major software</li>
                            <li>Regular updates included</li>
                            <li>Premium support for 6 months</li>
                        </ul>
                    </div>

                    <?php if($seller && $sellerApplication && $storeSlug): ?>
                        <div class="border-t border-gray-200 pt-6 mt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Seller Information</h3>
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-12 w-12 bg-gray-200 rounded-full overflow-hidden">
                                    <?php if($seller->store_image): ?>
                                        <img src="<?php echo e(asset('storage/' . $seller->store_image)); ?>"
                                            alt="<?php echo e($sellerApplication->business_name); ?>"
                                            class="h-full w-full object-cover">
                                    <?php else: ?>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-full w-full text-gray-400 p-2"
                                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                                        </svg>
                                    <?php endif; ?>
                                </div>
                                <div class="ml-4">
                                    <h4 class="text-sm font-medium text-gray-900"><?php echo e($sellerApplication->business_name); ?>

                                    </h4>
                                    <a href="<?php echo e(route('store.show', $storeSlug)); ?>"
                                        class="text-sm text-[#710d17] hover:text-[#9a2c39]">Visit Store</a>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="mt-8 flex flex-col sm:flex-row gap-4">
                        <button
                            class="w-full sm:w-auto bg-[#710d17] hover:bg-[#9a2c39] text-white font-medium py-3 px-8 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17] transition-colors">
                            Buy Now
                        </button>
                        <button
                            class="w-full sm:w-auto border border-[#710d17] text-[#710d17] hover:bg-[#fcefcc]/50 font-medium py-3 px-8 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17] transition-colors">
                            Add to Cart
                        </button>
                    </div>
                </div>
            </div>

            <!-- Related products section -->
            <div class="mt-16">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Related Products</h2>

                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                    <?php $__currentLoopData = $relatedProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedProduct): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="overflow-hidden rounded-lg border bg-white shadow-sm transition-all hover:shadow-md">
                            <a href="<?php echo e(route('products.show', $relatedProduct->slug ?? $relatedProduct->id)); ?>"
                                class="block">
                                <div class="grid aspect-video w-full overflow-hidden justify-center items-center">
                                    <?php if($relatedProduct->image && Storage::exists('public/' . $relatedProduct->image)): ?>
                                        <img src="<?php echo e(asset($relatedProduct->image)); ?>" alt="<?php echo e($relatedProduct->name); ?>"
                                            class="h-full w-full object-cover transition-transform hover:scale-105">
                                    <?php else: ?>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-gray-400"
                                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                                        </svg>
                                    <?php endif; ?>
                                </div>
                                <div class="p-4">
                                    <div class="flex justify-between items-center mb-2">
                                        <h3 class="text-lg font-bold line-clamp-1"><?php echo e($relatedProduct->name); ?></h3>
                                        <div class="flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                viewBox="0 0 24 24" fill="#f59e0b" stroke="#f59e0b" stroke-width="1"
                                                stroke-linecap="round" stroke-linejoin="round" class="mr-1">
                                                <polygon
                                                    points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                                </polygon>
                                            </svg>
                                            <span class="text-sm font-medium"><?php echo e($relatedProduct->rating); ?></span>
                                        </div>
                                    </div>
                                    <p class="text-xl font-bold text-[#710d17] mb-2">
                                        $<?php echo e(number_format($relatedProduct->price, 2)); ?></p>
                                    <p class="text-sm text-zinc-600 mb-4 line-clamp-2"><?php echo e($relatedProduct->description); ?>

                                    </p>
                                </div>
                            </a>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\hermosart\resources\views/product-detail.blade.php ENDPATH**/ ?>