<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Service extends Model
{
    protected $keyType = 'string';
    public $incrementing = false;

    protected $casts = [
        'features' => 'array',
    ];

    protected $fillable = ['category', 'title', 'slug', 'description', 'features', 'icon', 'professional_id'];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            // Generate UUID if not provided
            if (empty($model->id)) {
                $model->id = (string) Str::uuid();
            }

            // Generate slug if not provided
            if (empty($model->slug)) {
                $model->slug = $model->generateUniqueSlug($model->title);
            }
        });

        static::updating(function ($model) {
            // Update slug if title has changed
            if ($model->isDirty('title') && !$model->isDirty('slug')) {
                $model->slug = $model->generateUniqueSlug($model->title);
            }
        });
    }

    /**
     * Generate a unique slug based on the service's title.
     *
     * @param string $title
     * @return string
     */
    protected function generateUniqueSlug($title)
    {
        $slug = Str::slug($title);
        $originalSlug = $slug;
        $count = 1;

        // Make sure the slug is unique
        while (static::where('slug', $slug)->where('id', '!=', $this->id)->exists()) {
            $slug = $originalSlug . '-' . $count++;
        }

        return $slug;
    }

    public function category()
    {
        return $this->belongsTo(Category::class, 'category', 'id');
    }

    /**
     * Get the professional that offers the service.
     */
    public function professional()
    {
        return $this->belongsTo(Professional::class);
    }
}