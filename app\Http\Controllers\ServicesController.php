<?php

namespace App\Http\Controllers;

use App\Models\Service;
use App\Models\CaseStudy;
use Illuminate\Http\Request;

class ServicesController extends Controller
{
    /**
     * Show the services page.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        // Group services by category
        $services = Service::all()->groupBy('category')->map(function ($group) {
            return $group->toArray();
        })->toArray();

        $caseStudies = CaseStudy::all()->toArray();

        return view('services', compact('services', 'caseStudies'));
    }

    /**
     * Show a specific service's details.
     *
     * @param  string  $slug
     * @return \Illuminate\View\View
     */
    public function show($slug)
    {
        // Find the service by ID or slug
        $service = Service::where('id', $slug)
            ->orWhere('slug', $slug)
            ->firstOrFail();

        // Get related services from the same category
        $relatedServices = Service::where('category', $service->category)
            ->where('id', '!=', $service->id)
            ->take(3)
            ->get();

        // Get case studies related to this service category
        $relatedCaseStudies = CaseStudy::where('category', $service->category)
            ->take(3)
            ->get();

        return view('service-detail', compact('service', 'relatedServices', 'relatedCaseStudies'));
    }
}