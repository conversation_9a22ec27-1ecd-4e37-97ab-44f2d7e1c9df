<?php

namespace Database\Seeders;

use App\Models\Service;
use App\Models\CaseStudy;
use App\Models\User;
use App\Models\Professional;
use Illuminate\Database\Seeder;

class ServicesSeeder extends Seeder
{
    public function run(): void
    {
        // Get professional users
        $professionalUser = User::where('email', '<EMAIL>')->first();
        $proSellerUser = User::where('email', '<EMAIL>')->first();

        if (!$professionalUser || !$proSellerUser) {
            $this->command->info('Required users not found. Please run UserSeeder first.');
            return;
        }

        // Get professional records
        $professional = Professional::where('user_id', $professionalUser->id)->first();
        $proSeller = Professional::where('user_id', $proSellerUser->id)->first();

        if (!$professional || !$proSeller) {
            // Create professional records if they don't exist
            if (!$professional) {
                $professional = new Professional();
                $professional->id = (string) \Illuminate\Support\Str::uuid();
                $professional->user_id = $professionalUser->id;
                $professional->name = $professionalUser->name;
                $professional->title = 'Web Developer';
                $professional->skills = ['HTML', 'CSS', 'JavaScript'];
                $professional->description = 'Professional web developer';
                $professional->experience = '5 years';
                $professional->rating = 4.8;
                $professional->projects = 0;
                $professional->category = 'web';
                $professional->is_aiverified = true;
                $professional->save();
            }

            if (!$proSeller) {
                $proSeller = new Professional();
                $proSeller->id = (string) \Illuminate\Support\Str::uuid();
                $proSeller->user_id = $proSellerUser->id;
                $proSeller->name = $proSellerUser->name;
                $proSeller->title = 'Data Scientist';
                $proSeller->skills = ['Python', 'R', 'Machine Learning'];
                $proSeller->description = 'Professional data scientist';
                $proSeller->experience = '7 years';
                $proSeller->rating = 4.9;
                $proSeller->projects = 0;
                $proSeller->category = 'data';
                $proSeller->is_aiverified = true;
                $proSeller->save();
            }
        }

        // Services
        Service::create([
            'professional_id' => $professional->id,
            'category' => 'web',
            'title' => 'Web Development',
            'description' => 'Custom websites and web applications built by expert developers.',
            'features' => [
                'Homepage Development',
                'E-commerce Solutions',
                'Web Applications',
                'CMS Integration',
                'Responsive Design',
            ],
            'icon' => 'code',
        ]);
        Service::create([
            'professional_id' => $professional->id,
            'category' => 'web',
            'title' => 'Startup Consulting',
            'description' => 'Strategic guidance for tech startups and digital businesses.',
            'features' => [
                'Launch Strategy',
                'Business Plan Development',
                'Market Analysis',
                'Growth Hacking',
                'Investor Pitch Preparation',
            ],
            'icon' => 'code',
        ]);
        Service::create([
            'professional_id' => $proSeller->id,
            'category' => 'data',
            'title' => 'Data Scraping',
            'description' => 'Extract valuable data from websites and online sources.',
            'features' => [
                'Google Maps Data Extraction',
                'E-commerce Product Scraping',
                'Social Media Data Collection',
                'Web Scraping Automation',
                'Custom Data Extraction',
            ],
            'icon' => 'database',
        ]);
        Service::create([
            'professional_id' => $proSeller->id,
            'category' => 'data',
            'title' => 'Data Analysis',
            'description' => 'Transform raw data into actionable business insights.',
            'features' => [
                'In-Depth Data Analysis',
                'Statistical Modeling',
                'Predictive Analytics',
                'Data Cleaning & Preparation',
                'Expert Data Strategy',
            ],
            'icon' => 'database',
        ]);
        Service::create([
            'professional_id' => $proSeller->id,
            'category' => 'data',
            'title' => 'Data Visualization',
            'description' => 'Create compelling visual representations of complex data.',
            'features' => [
                'Custom Data Dashboards',
                'Interactive Charts & Graphs',
                'Real-time Data Visualization',
                'Business Intelligence Tools',
                'Reporting Automation',
            ],
            'icon' => 'database',
        ]);
        Service::create([
            'professional_id' => $proSeller->id,
            'category' => 'ai',
            'title' => 'AI Automation',
            'description' => 'Streamline your business processes with custom AI solutions.',
            'features' => [
                'Custom Chatbots',
                'Workflow Automation',
                'AI Integration',
                'Natural Language Processing',
                'Machine Learning Solutions',
            ],
            'icon' => 'cpu',
        ]);
        Service::create([
            'professional_id' => $proSeller->id,
            'category' => 'ai',
            'title' => 'AI Model Development',
            'description' => 'Custom AI models tailored to your specific business needs.',
            'features' => [
                'Computer Vision Systems',
                'Predictive Models',
                'Recommendation Engines',
                'Natural Language Understanding',
                'Custom AI Solutions',
            ],
            'icon' => 'cpu',
        ]);
        Service::create([
            'professional_id' => $professional->id,
            'category' => 'design',
            'title' => 'Brand Identity',
            'description' => 'Create a distinctive and memorable brand presence.',
            'features' => [
                'Logo Design',
                'Brand Guidelines',
                'Visual Identity Systems',
                'Brand Strategy',
                'Rebranding',
            ],
            'icon' => 'palette',
        ]);
        Service::create([
            'professional_id' => $professional->id,
            'category' => 'design',
            'title' => 'Editorial Layout',
            'description' => 'Professional design for print and digital publications.',
            'features' => [
                'Business Cards & Stationery',
                'Menu Design',
                'Brochure Layout',
                'Poster Design',
                'Magazine & Book Layout',
            ],
            'icon' => 'palette',
        ]);
        Service::create([
            'professional_id' => $professional->id,
            'category' => 'design',
            'title' => 'UI/UX Design',
            'description' => 'Create intuitive and engaging user experiences.',
            'features' => [
                'User Interface Design',
                'User Experience Research',
                'Wireframing & Prototyping',
                'Mobile App Design',
                'Web Application Design',
            ],
            'icon' => 'palette',
        ]);

        // Case Studies
        CaseStudy::create([
            'title' => 'E-commerce Platform Redesign',
            'client' => 'Fashion Retailer',
            'description' => 'Increased conversion rates by 35% through a complete redesign of the shopping experience.',
            'image' => 'images/case-study-1.jpg',
            'category' => 'web',
        ]);
        CaseStudy::create([
            'title' => 'Customer Data Analysis',
            'client' => 'Financial Services Company',
            'description' => 'Identified $2.3M in potential revenue through advanced customer segmentation and analysis.',
            'image' => 'images/case-study-2.jpg',
            'category' => 'data',
        ]);
        CaseStudy::create([
            'title' => 'AI-Powered Customer Service',
            'client' => 'Telecommunications Provider',
            'description' => 'Reduced support costs by 40% while improving customer satisfaction scores.',
            'image' => 'images/case-study-3.jpg',
            'category' => 'ai',
        ]);
    }
}